import React, { useState, useEffect, useMemo } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { Card } from "primereact/card";
import { But<PERSON> } from "primereact/button";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import APIServices from "../../../../../service/APIService";
import { API } from "../../../../../constants/api_url";

const MSIAuditProcessChart = ({ supplyData = [] }) => {
  const [activeMode, setActiveMode] = useState(true);
  const [chartData, setChartData] = useState([]);

  // Process supply data to extract audit process information
  useEffect(() => {
    const fetchAuditProcessData = async () => {
      try {
        const filter = {
          order: ["created_on DESC"],
          include: [
            { relation: "supplierActions" },
            { relation: "auditorAssignmentSubmission" },
            { relation: "supplierAssignmentSubmission" }
          ]
        };

        const res = await APIServices.get(
          API.Supplier_assessment_assignment + `?filter=${encodeURIComponent(JSON.stringify(filter))}`
        );

        const allAssignments = Array.isArray(res.data) ? res.data : [];

        // Use the same logic as Dashboard.js for counting audit stages

        // 1. Audit Scheduled (Planned) - same as Dashboard.js line 854
        // Count suppliers without auditorAssignmentSubmission or with type 0
        const auditScheduled = allAssignments.filter(x =>
          !x?.auditorAssignmentSubmission ||
          (x?.auditorAssignmentSubmission && x?.auditorAssignmentSubmission.type === 0)
        ).length;

        // 2. Audits Conducted (Completed) - same as Dashboard.js line 867
        // Count suppliers with auditorAssignmentSubmission type !== 0 (includes type 1 and 2)
        const auditConducted = allAssignments.filter(x =>
          x?.auditorAssignmentSubmission &&
          (x?.auditorAssignmentSubmission.type !== 0)
        ).length;

        // 3. Audit Reports Released (Approved ones from completed audits)
        // Type 2 = approved/reviewed reports
        const reportsReleased = allAssignments.filter(x =>
          x?.auditorAssignmentSubmission &&
          x?.auditorAssignmentSubmission.type === 2
        ).length;

        // 4. Reports Acknowledged by Supplier (suppliers with actions)
        // Suppliers who have received reports and have actions assigned
        const reportsAcknowledged = allAssignments.filter(x =>
          x?.auditorAssignmentSubmission &&
          (x?.auditorAssignmentSubmission.type === 1 || x?.auditorAssignmentSubmission.type === 2) &&
          Array.isArray(x.supplierActions) && x.supplierActions.length > 0
        ).length;

        // 5. Action Plan Submitted by Supplier (suppliers with submitted/completed actions)
        // Suppliers who have submitted or completed their action plans
        const actionPlanSubmitted = allAssignments.filter(x =>
          x?.auditorAssignmentSubmission &&
          (x?.auditorAssignmentSubmission.type === 1 || x?.auditorAssignmentSubmission.type === 2) &&
          Array.isArray(x.supplierActions) && x.supplierActions.length > 0 &&
          x.supplierActions.some(action =>
            action.status === 'submitted' || action.status === 'completed'
          )
        ).length;

        const processedData = [
          { stage: "Audit Scheduled", count: auditScheduled, color: "#6366F1" },
          { stage: "Audits Conducted", count: auditConducted, color: "#3B82F6" },
          { stage: "Audit Reports Released", count: reportsReleased, color: "#22C55E" },
          { stage: "Reports Acknowledged by Supplier", count: reportsAcknowledged, color: "#F59E0B" },
          { stage: "Action Plan Submitted by Supplier", count: actionPlanSubmitted, color: "#8B5CF6" }
        ];

        console.log('MSIAuditProcessChart - Final processed data:', processedData);
        console.log('MSIAuditProcessChart - Audit counts:', {
          auditScheduled,
          auditConducted,
          reportsReleased,
          reportsAcknowledged,
          actionPlanSubmitted,
          totalAssignments: allAssignments.length
        });
        setChartData(processedData);
      } catch (error) {
        console.error('Error fetching audit process data:', error);
        // Fallback to mock data on error
        const mockData = [
          { stage: "Audit Scheduled", count: 25, color: "#6366F1" },
          { stage: "Audits Conducted", count: 22, color: "#3B82F6" },
          { stage: "Audit Reports Released", count: 20, color: "#22C55E" },
          { stage: "Reports Acknowledged by Supplier", count: 18, color: "#F59E0B" },
          { stage: "Action Plan Submitted by Supplier", count: 15, color: "#8B5CF6" }
        ];
        setChartData(mockData);
      }
    };

    fetchAuditProcessData();
  }, []);

  // Highcharts configuration
  const chartOptions = useMemo(() => ({
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'Arial, sans-serif'
      }
    },
    title: {
      text: 'Audit Process Analytics',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
        color: '#333'
      }
    },
    xAxis: {
      categories: chartData.map(item => item.stage),
      labels: {
        style: {
          fontSize: '11px',
          color: '#666'
        },
        formatter: function() {
          // Word wrap long labels
          const words = this.value.split(' ');
          if (words.length > 3) {
            const mid = Math.ceil(words.length / 2);
            return words.slice(0, mid).join(' ') + '<br/>' + words.slice(mid).join(' ');
          }
          return this.value;
        },
        useHTML: true
      },
      gridLineWidth: 0,
      lineColor: '#e0e0e0'
    },
    yAxis: {
      title: {
        text: 'Number of Suppliers',
        style: {
          fontSize: '12px',
          color: '#666'
        }
      },
      labels: {
        style: {
          fontSize: '11px',
          color: '#666'
        }
      },
      gridLineColor: '#f0f0f0',
      min: 0
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ccc',
      borderRadius: 8,
      shadow: true,
      style: {
        fontSize: '12px'
      },
      formatter: function() {
        return `<b>${this.x}</b><br/>Count: <b>${this.y}</b>`;
      }
    },
    plotOptions: {
      column: {
        maxPointWidth: 50,
        borderWidth: 0,
        borderRadius: 4,
        dataLabels: {
          enabled: true,
          style: {
            fontSize: '11px',
            fontWeight: 'bold',
            color: '#333'
          }
        }
      }
    },
    legend: {
      enabled: false
    },
    series: [{
      name: 'Suppliers',
      data: chartData.map(item => ({
        y: item.count,
        color: item.color
      })),
      colorByPoint: true
    }],
    credits: {
      enabled: false
    },
    responsive: {
      rules: [{
        condition: {
          maxWidth: 500
        },
        chartOptions: {
          plotOptions: {
            column: {
              maxPointWidth: 35
            }
          },
          xAxis: {
            labels: {
              style: {
                fontSize: '10px'
              }
            }
          }
        }
      }]
    }
  }), [chartData]);

  return (
    <Card>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" }}>
        <h3 style={{ margin: 0, borderBottom: "2px solid #28a745", paddingBottom: "5px" }}>
          Audit Process Analytics
        </h3>
        <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
          <Button
            icon={activeMode ? "pi pi-table" : "pi pi-chart-bar"}
            className="p-button-text"
            onClick={() => setActiveMode(!activeMode)}
            tooltip={activeMode ? "Switch to Table View" : "Switch to Chart View"}
          />
        </div>
      </div>

      {activeMode ? (
        <div style={{ height: '400px', width: '100%' }}>
          <HighchartsReact
            highcharts={Highcharts}
            options={chartOptions}
            containerProps={{ style: { height: '100%', width: '100%' } }}
          />
        </div>
      ) : (
        <div style={{ padding: "20px" }}>
          <DataTable value={chartData} paginator rows={10} sortMode="multiple">
            <Column field="stage" header="Audit Stage" sortable />
            <Column 
              field="count" 
              header="Count" 
              sortable 
              body={(rowData) => (
                <span style={{ 
                  color: rowData.color, 
                  fontWeight: 'bold' 
                }}>
                  {rowData.count}
                </span>
              )}
            />
          </DataTable>
        </div>
      )}

      {/* Summary */}
      <div style={{ 
        marginTop: "20px", 
        padding: "15px", 
        backgroundColor: "#f8f9fa", 
        borderRadius: "5px" 
      }}>
        <h4 style={{ margin: "0 0 10px 0" }}>Audit Process Summary</h4>
        <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(120px, 1fr))", gap: "15px" }}>
          {chartData.map((item, index) => (
            <div key={index} style={{ textAlign: "center" }}>
              <p style={{ margin: "5px 0", fontSize: "12px", color: "#666" }}>{item.stage}</p>
              <p style={{ margin: "0", fontSize: "18px", fontWeight: "bold", color: item.color }}>
                {item.count}
              </p>
            </div>
          ))}
        </div>
        
        {/* Process completion rate */}
        <div style={{ marginTop: "15px", textAlign: "center" }}>
          <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Overall Process Completion Rate</p>
          <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#8B5CF6" }}>
            {chartData.length > 0 && chartData[0].count > 0 ? 
              ((chartData[chartData.length - 1]?.count || 0) / chartData[0].count * 100).toFixed(1) : 0}%
          </p>
        </div>
      </div>
    </Card>
  );
};

export default MSIAuditProcessChart;
